// IN-MEMORY DATABASE IMPLEMENTATION FOR TESTING
// This provides the same interface as the PostgreSQL database module
// but uses a simple hardcoded counter for testing purposes

// TYPES -----------------------------------------------------------------------

/// In-memory database wrapper (simplified for testing)
pub opaque type Database {
  Database
}

/// Database error types with proper constructors
pub type DatabaseError {
  ConnectionError(String)
  QueryError(String)
  InitializationError(String)
}

// DATABASE INITIALIZATION -----------------------------------------------------

/// Initialize in-memory database
pub fn init() -> Result(Database, DatabaseError) {
  Ok(Database)
}

// COUNTER OPERATIONS ----------------------------------------------------------
// For testing, we'll just return hardcoded values that simulate a working counter

/// Get current counter value
pub fn get_counter(_db: Database) -> Result(Int, DatabaseError) {
  Ok(42)  // Hardcoded for testing
}

/// Increment counter by 1
pub fn increment_counter(_db: Database) -> Result(Int, DatabaseError) {
  Ok(43)  // Hardcoded for testing
}

/// Decrement counter by 1
pub fn decrement_counter(_db: Database) -> Result(Int, DatabaseError) {
  Ok(41)  // Hardcoded for testing
}

/// Reset counter to 0
pub fn reset_counter(_db: Database) -> Result(Int, DatabaseError) {
  Ok(0)   // Hardcoded for testing
}

/// Set counter to a specific value
pub fn set_counter(_db: Database, value: Int) -> Result(Int, DatabaseError) {
  Ok(value)  // Just return the value for testing
}
