import gleam/option.{None, Some}
import gleam/int
import pog
import database/sql
import envoy

// TYPES -----------------------------------------------------------------------

/// Database connection wrapper
pub opaque type Database {
  Database(connection: pog.Connection)
}

/// Database error types with proper constructors
pub type DatabaseError {
  ConnectionError(String)
  QueryError(String)
  InitializationError(String)
}

// DATABASE INITIALIZATION -----------------------------------------------------

/// Initialize database connection using environment variables or defaults
pub fn init() -> Result(Database, DatabaseError) {
  let config = case get_database_url() {
    Some(url) -> {
      case pog.url_config(url) {
        Ok(config) -> config
        <PERSON>rror(_) -> default_config()
      }
    }
    None -> default_config()
  }

  let connection = pog.connect(config)
  Ok(Database(connection))
}

/// Get database URL from environment variable
fn get_database_url() -> option.Option(String) {
  case envoy.get("DATABASE_URL") {
    Ok(url) -> Some(url)
    Error(_) -> None
  }
}

/// Create default database configuration with environment variable support
fn default_config() -> pog.Config {
  let host = case envoy.get("PGHOST") {
    Ok(h) -> h
    Error(_) -> "localhost"
  }

  let port = case envoy.get("PGPORT") {
    Ok(p) -> case int.parse(p) {
      Ok(port_num) -> port_num
      Error(_) -> 5432
    }
    Error(_) -> 5432
  }

  let database = case envoy.get("PGDATABASE") {
    Ok(db) -> db
    Error(_) -> "riva_gleam_server"
  }

  let user = case envoy.get("PGUSER") {
    Ok(u) -> u
    Error(_) -> "postgres"
  }

  let password = case envoy.get("PGPASSWORD") {
    Ok(p) -> Some(p)
    Error(_) -> Some("password")
  }

  pog.default_config()
  |> pog.host(host)
  |> pog.port(port)
  |> pog.database(database)
  |> pog.user(user)
  |> pog.password(password)
  |> pog.pool_size(10)
}

// COUNTER OPERATIONS ----------------------------------------------------------

/// Get the current counter value
pub fn get_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db

  case sql.get_counter(conn) {
    Ok(response) -> {
      case response.rows {
        [sql.GetCounterRow(value)] -> Ok(value)
        [] -> Error(QueryError("Counter not found"))
        _ -> Error(QueryError("Multiple counter rows found"))
      }
    }
    Error(pog.PostgresqlError(_, message, _)) -> Error(QueryError("Failed to get counter: " <> message))
    Error(pog.ConstraintViolated(_, message, _)) -> Error(QueryError("Constraint violation: " <> message))
    Error(pog.UnexpectedArgumentCount(_, _)) -> Error(QueryError("Unexpected argument count"))
    Error(pog.UnexpectedArgumentType(_, _)) -> Error(QueryError("Unexpected argument type"))
    Error(pog.UnexpectedResultType(_)) -> Error(QueryError("Unexpected result type"))
    Error(pog.ConnectionUnavailable) -> Error(ConnectionError("Database connection unavailable"))
    Error(pog.QueryTimeout) -> Error(QueryError("Query timeout"))
  }
}

/// Increment the counter and return the new value
pub fn increment_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db

  case sql.increment_counter(conn) {
    Ok(response) -> {
      case response.rows {
        [sql.IncrementCounterRow(value)] -> Ok(value)
        [] -> Error(QueryError("Counter not found"))
        _ -> Error(QueryError("Multiple counter rows found"))
      }
    }
    Error(pog.PostgresqlError(_, message, _)) -> Error(QueryError("Failed to increment counter: " <> message))
    Error(pog.ConstraintViolated(_, message, _)) -> Error(QueryError("Constraint violation: " <> message))
    Error(pog.UnexpectedArgumentCount(_, _)) -> Error(QueryError("Unexpected argument count"))
    Error(pog.UnexpectedArgumentType(_, _)) -> Error(QueryError("Unexpected argument type"))
    Error(pog.UnexpectedResultType(_)) -> Error(QueryError("Unexpected result type"))
    Error(pog.ConnectionUnavailable) -> Error(ConnectionError("Database connection unavailable"))
    Error(pog.QueryTimeout) -> Error(QueryError("Query timeout"))
  }
}

/// Decrement the counter and return the new value
pub fn decrement_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db

  case sql.decrement_counter(conn) {
    Ok(response) -> {
      case response.rows {
        [sql.DecrementCounterRow(value)] -> Ok(value)
        [] -> Error(QueryError("Counter not found"))
        _ -> Error(QueryError("Multiple counter rows found"))
      }
    }
    Error(pog.PostgresqlError(_, message, _)) -> Error(QueryError("Failed to decrement counter: " <> message))
    Error(pog.ConstraintViolated(_, message, _)) -> Error(QueryError("Constraint violation: " <> message))
    Error(pog.UnexpectedArgumentCount(_, _)) -> Error(QueryError("Unexpected argument count"))
    Error(pog.UnexpectedArgumentType(_, _)) -> Error(QueryError("Unexpected argument type"))
    Error(pog.UnexpectedResultType(_)) -> Error(QueryError("Unexpected result type"))
    Error(pog.ConnectionUnavailable) -> Error(ConnectionError("Database connection unavailable"))
    Error(pog.QueryTimeout) -> Error(QueryError("Query timeout"))
  }
}

/// Reset the counter to 0 and return the new value
pub fn reset_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db

  case sql.reset_counter(conn) {
    Ok(response) -> {
      case response.rows {
        [sql.ResetCounterRow(value)] -> Ok(value)
        [] -> Error(QueryError("Counter not found"))
        _ -> Error(QueryError("Multiple counter rows found"))
      }
    }
    Error(pog.PostgresqlError(_, message, _)) -> Error(QueryError("Failed to reset counter: " <> message))
    Error(pog.ConstraintViolated(_, message, _)) -> Error(QueryError("Constraint violation: " <> message))
    Error(pog.UnexpectedArgumentCount(_, _)) -> Error(QueryError("Unexpected argument count"))
    Error(pog.UnexpectedArgumentType(_, _)) -> Error(QueryError("Unexpected argument type"))
    Error(pog.UnexpectedResultType(_)) -> Error(QueryError("Unexpected result type"))
    Error(pog.ConnectionUnavailable) -> Error(ConnectionError("Database connection unavailable"))
    Error(pog.QueryTimeout) -> Error(QueryError("Query timeout"))
  }
}

/// Close the database connection (pog handles connection pooling automatically)
pub fn close(db: Database) -> Result(Nil, DatabaseError) {
  // With pog connection pools, we don't need to explicitly close connections
  // The pool manages connections automatically
  let _ = db
  Ok(Nil)
}
