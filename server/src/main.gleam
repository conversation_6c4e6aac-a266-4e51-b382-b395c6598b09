import api_server
import gleam/io

// SERVER MAIN ENTRY POINT ----------------------------------------------------

pub fn main() -> Nil {
  io.println("🚀 Starting Riva Gleam API Server...")
  
  case api_server.start_server() {
    Ok(_) -> {
      io.println("✅ API server started successfully")
      io.println("📡 Server ready to handle requests")
    }
    Error(msg) -> {
      io.println("❌ Failed to start server: " <> msg)
    }
  }
}
