import wisp
import wisp/wisp_mist
import mist
import gleam/http.{Get, Post}
import gleam/string
import gleam/io
import gleam/result
import gleam/option.{None, Some}
import gleam/erlang/process
import riva_gleam_shared
import database_memory as database

// WISP API SERVER IMPLEMENTATION =============================================

/// Start the Wisp API server using Mist
pub fn start_server() -> Result(Nil, String) {
  // Initialize database connection
  case database.init() {
    Ok(db) -> {
      io.println("✅ Database connection established")

      // Create request handler with database context
      let handler = handle_request(db, _)

      // Configure Wisp
      wisp.configure_logger()
      io.println("🚀 Starting Wisp server on port 8000...")

      // Secret key for signing cookies and other sensitive data
      let secret_key_base = "your-secret-key-base-must-be-at-least-64-bytes-long-for-security"

      // Start the server using Mist
      let assert Ok(_) =
        handler
        |> wisp_mist.handler(secret_key_base)
        |> mist.new
        |> mist.port(8000)
        |> mist.start

      io.println("✅ Wisp server started successfully on http://localhost:8000")

      // Keep the server running
      process.sleep_forever()

      Ok(Nil)
    }
    Error(db_error) -> {
      case db_error {
        database.ConnectionError(msg) -> Error("Database connection failed: " <> msg)
        database.QueryError(msg) -> Error("Database query error: " <> msg)
        database.InitializationError(msg) -> Error("Database initialization failed: " <> msg)
      }
    }
  }
}

// REQUEST HANDLING ============================================================

/// Main request handler that routes requests to appropriate endpoints
fn handle_request(db: database.Database, req: wisp.Request) -> wisp.Response {
  // Add CORS headers to all responses
  use req <- wisp.handle_head(req)
  use <- add_cors_headers()

  case wisp.path_segments(req) {
    ["api", "counter"] -> handle_counter_endpoint(db, req)
    _ -> wisp.not_found()
  }
}

/// Add CORS headers to allow cross-origin requests from the frontend
fn add_cors_headers(handle_request: fn() -> wisp.Response) -> wisp.Response {
  let response = handle_request()

  response
  |> wisp.set_header("access-control-allow-origin", "*")
  |> wisp.set_header("access-control-allow-methods", "GET, POST, OPTIONS")
  |> wisp.set_header("access-control-allow-headers", "Content-Type")
}

/// Handle requests to the /api/counter endpoint
fn handle_counter_endpoint(db: database.Database, req: wisp.Request) -> wisp.Response {
  case req.method {
    Get -> handle_get_counter(db, req)
    Post -> handle_post_counter(db, req)
    _ -> wisp.method_not_allowed([Get, Post])
  }
}

// ENDPOINT IMPLEMENTATIONS ===================================================

/// Handle GET /api/counter - return current counter state
fn handle_get_counter(db: database.Database, _req: wisp.Request) -> wisp.Response {
  case database.get_counter(db) {
    Ok(count) -> {
      let response = riva_gleam_shared.counter_response(
        count,
        get_current_timestamp(),
        None,
      )

      let json_string = riva_gleam_shared.encode_counter_response_string(response)

      wisp.response(200)
      |> wisp.set_header("content-type", "application/json")
      |> wisp.string_body(json_string)
    }
    Error(db_error) -> {
      io.println("❌ Database error in GET /api/counter: " <> string.inspect(db_error))

      wisp.response(500)
      |> wisp.set_header("content-type", "application/json")
      |> wisp.string_body("{\"error\":\"Internal server error\"}")
    }
  }
}

/// Handle POST /api/counter - process counter operations
fn handle_post_counter(db: database.Database, req: wisp.Request) -> wisp.Response {
  use json_string <- wisp.require_string_body(req)

  case riva_gleam_shared.decode_counter_request_from_json(json_string) {
    Ok(request) -> {
      case process_counter_request(db, request) {
        Ok(response) -> {
          let json_response = riva_gleam_shared.encode_counter_response_string(response)

          wisp.response(200)
          |> wisp.set_header("content-type", "application/json")
          |> wisp.string_body(json_response)
        }
        Error(error_msg) -> {
          io.println("❌ Error processing counter request: " <> error_msg)

          wisp.response(500)
          |> wisp.set_header("content-type", "application/json")
          |> wisp.string_body("{\"error\":\"" <> error_msg <> "\"}")
        }
      }
    }
    Error(decode_error) -> {
      io.println("❌ Failed to decode request: " <> decode_error)

      wisp.response(400)
      |> wisp.set_header("content-type", "application/json")
      |> wisp.string_body("{\"error\":\"Invalid request format\"}")
    }
  }
}

// BUSINESS LOGIC ==============================================================

/// Process a counter request and return the appropriate response
fn process_counter_request(
  db: database.Database,
  request: riva_gleam_shared.CounterRequest,
) -> Result(riva_gleam_shared.CounterResponse, String) {
  // Get the current value before the operation for previous_count
  let previous_count = case database.get_counter(db) {
    Ok(count) -> Some(count)
    Error(_) -> None
  }

  riva_gleam_shared.match_counter_request(
    request,
    // on_increment
    fn() {
      case database.increment_counter(db) {
        Ok(new_count) -> Ok(riva_gleam_shared.counter_response(
          new_count,
          get_current_timestamp(),
          previous_count,
        ))
        Error(db_error) -> Error("Failed to increment counter: " <> string.inspect(db_error))
      }
    },
    // on_decrement
    fn() {
      case database.decrement_counter(db) {
        Ok(new_count) -> Ok(riva_gleam_shared.counter_response(
          new_count,
          get_current_timestamp(),
          previous_count,
        ))
        Error(db_error) -> Error("Failed to decrement counter: " <> string.inspect(db_error))
      }
    },
    // on_reset
    fn() {
      case database.reset_counter(db) {
        Ok(new_count) -> Ok(riva_gleam_shared.counter_response(
          new_count,
          get_current_timestamp(),
          previous_count,
        ))
        Error(db_error) -> Error("Failed to reset counter: " <> string.inspect(db_error))
      }
    },
    // on_set_value
    fn(value) {
      // For now, we'll implement set_value by resetting and then incrementing
      // In a real implementation, you'd add a set_counter function to the database module
      case database.reset_counter(db) {
        Ok(_) -> {
          case set_counter_to_value(db, value) {
            Ok(final_count) -> Ok(riva_gleam_shared.counter_response(
              final_count,
              get_current_timestamp(),
              previous_count,
            ))
            Error(error) -> Error("Failed to set counter value: " <> error)
          }
        }
        Error(db_error) -> Error("Failed to reset counter for set operation: " <> string.inspect(db_error))
      }
    },
    // on_get_stats
    fn() {
      // For now, just return the current count as stats
      // In a real implementation, you'd have more comprehensive stats
      case database.get_counter(db) {
        Ok(count) -> Ok(riva_gleam_shared.counter_response(
          count,
          get_current_timestamp(),
          None,
        ))
        Error(db_error) -> Error("Failed to get counter stats: " <> string.inspect(db_error))
      }
    },
  )
}

// UTILITY FUNCTIONS ===========================================================

/// Set counter to a specific value by incrementing from 0
fn set_counter_to_value(db: database.Database, target_value: Int) -> Result(Int, String) {
  case target_value {
    0 -> Ok(0)  // Already at 0 after reset
    value if value > 0 -> {
      // Increment to reach the target value
      increment_multiple_times(db, value)
    }
    value if value < 0 -> {
      // Decrement to reach the target value
      decrement_multiple_times(db, -value)
    }
    _ -> Error("Invalid target value")
  }
}

/// Increment counter multiple times
fn increment_multiple_times(db: database.Database, times: Int) -> Result(Int, String) {
  case times {
    0 -> database.get_counter(db) |> result.map_error(string.inspect)
    n if n > 0 -> {
      case database.increment_counter(db) {
        Ok(_) -> increment_multiple_times(db, n - 1)
        Error(error) -> Error(string.inspect(error))
      }
    }
    _ -> Error("Invalid increment count")
  }
}

/// Decrement counter multiple times
fn decrement_multiple_times(db: database.Database, times: Int) -> Result(Int, String) {
  case times {
    0 -> database.get_counter(db) |> result.map_error(string.inspect)
    n if n > 0 -> {
      case database.decrement_counter(db) {
        Ok(_) -> decrement_multiple_times(db, n - 1)
        Error(error) -> Error(string.inspect(error))
      }
    }
    _ -> Error("Invalid decrement count")
  }
}

/// Get current timestamp (simplified implementation)
fn get_current_timestamp() -> String {
  // For now, return a static timestamp
  // In a real implementation, you'd use gleam/erlang or a time library
  "2024-06-28T12:00:00Z"
}
