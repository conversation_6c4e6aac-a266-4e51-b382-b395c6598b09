name = "riva_gleam_server"
version = "1.0.0"
description = "Riva Gleam - Wisp API Server"
licences = ["Apache-2.0"]

# Build configuration for server (Erlang target)
target = "erlang"

[dependencies]
gleam_stdlib = ">= 0.44.0 and < 2.0.0"
wisp = ">= 1.8.0 and < 2.0.0"
gleam_http = ">= 4.0.0 and < 5.0.0"
gleam_json = ">= 3.0.1 and < 4.0.0"
gleam_fetch = ">= 1.3.0 and < 2.0.0"
gleam_erlang = ">= 1.1.0 and < 2.0.0"
squirrel = ">= 3.0.0 and < 4.0.0"
pog = ">= 3.0.0 and < 4.0.0"
envoy = ">= 1.0.2 and < 2.0.0"
riva_gleam_shared = { path = "../shared" }
mist = ">= 5.0.1 and < 6.0.0"

[dev-dependencies]
gleeunit = ">= 1.0.0 and < 2.0.0"
