// API CLIENT MODULE ===========================================================
// HTTP communication with the Wisp server using shared types

import gleam/string
import lustre/effect.{type Effect}
import lustre_http
import riva_gleam_shared

// API CONFIGURATION -----------------------------------------------------------

const api_base_url = "http://localhost:8000"

// API EFFECTS -----------------------------------------------------------------

/// Get current counter value from server
pub fn get_counter(on_response: fn(Result(riva_gleam_shared.CounterResponse, String)) -> msg) -> Effect(msg) {
  let url = api_base_url <> "/api/counter"

  lustre_http.get(
    url,
    lustre_http.expect_text(fn(result) {
      case result {
        Ok(json_string) -> {
          case riva_gleam_shared.decode_counter_response_from_json(json_string) {
            Ok(counter_response) -> on_response(Ok(counter_response))
            Error(decode_error) -> on_response(Error("Decode error: " <> decode_error))
          }
        }
        Error(http_error) -> on_response(Error("HTTP error: " <> string.inspect(http_error)))
      }
    })
  )
}

/// Send counter operation to server
pub fn send_counter_request(
  counter_request: riva_gleam_shared.CounterRequest,
  on_response: fn(Result(riva_gleam_shared.CounterResponse, String)) -> msg
) -> Effect(msg) {
  let url = api_base_url <> "/api/counter"

  // Use the shared module's encoder to get JSON
  let json_body = riva_gleam_shared.encode_counter_request(counter_request)

  lustre_http.post(
    url,
    json_body,
    lustre_http.expect_text(fn(result) {
      case result {
        Ok(json_string) -> {
          case riva_gleam_shared.decode_counter_response_from_json(json_string) {
            Ok(counter_response) -> on_response(Ok(counter_response))
            Error(decode_error) -> on_response(Error("Decode error: " <> decode_error))
          }
        }
        Error(http_error) -> on_response(Error("HTTP error: " <> string.inspect(http_error)))
      }
    })
  )
}
