import gleam/int
import gleam/list
import gleam/option.{type Option, None, Some}
import lustre
import lustre/effect.{type Effect}
import lustre/element.{type Element}
import lustre/element/html

import button
import api
import riva_gleam_shared

// TYPES -----------------------------------------------------------------------

pub type Model {
  Model(
    count: Int,
    loading: Bool,
    error: Option(String),
    last_response: Option(riva_gleam_shared.CounterResponse)
  )
}

pub type Msg {
  // User actions
  Increment
  Decrement
  Reset
  LoadCounter

  // API responses
  CounterLoaded(Result(riva_gleam_shared.CounterResponse, String))
  CounterUpdated(Result(riva_gleam_shared.CounterResponse, String))
}

// INIT ------------------------------------------------------------------------

fn init(_flags) -> #(Model, Effect(Msg)) {
  let initial_model = Model(
    count: 0,
    loading: True,
    error: None,
    last_response: None
  )

  #(initial_model, api.get_counter(CounterLoaded))
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> #(Model, Effect(Msg)) {
  case msg {
    // User actions - send requests to server
    Increment -> {
      let loading_model = Model(..model, loading: True, error: None)
      let effect = api.send_counter_request(riva_gleam_shared.increment_request(), CounterUpdated)
      #(loading_model, effect)
    }

    Decrement -> {
      let loading_model = Model(..model, loading: True, error: None)
      let effect = api.send_counter_request(riva_gleam_shared.decrement_request(), CounterUpdated)
      #(loading_model, effect)
    }

    Reset -> {
      let loading_model = Model(..model, loading: True, error: None)
      let effect = api.send_counter_request(riva_gleam_shared.reset_request(), CounterUpdated)
      #(loading_model, effect)
    }

    LoadCounter -> {
      let loading_model = Model(..model, loading: True, error: None)
      #(loading_model, api.get_counter(CounterLoaded))
    }

    // API responses
    CounterLoaded(result) -> {
      case result {
        Ok(response) -> {
          let updated_model = Model(
            count: response.count,
            loading: False,
            error: None,
            last_response: Some(response)
          )
          #(updated_model, effect.none())
        }
        Error(error_msg) -> {
          let error_model = Model(..model, loading: False, error: Some(error_msg))
          #(error_model, effect.none())
        }
      }
    }

    CounterUpdated(result) -> {
      case result {
        Ok(response) -> {
          let updated_model = Model(
            count: response.count,
            loading: False,
            error: None,
            last_response: Some(response)
          )
          #(updated_model, effect.none())
        }
        Error(error_msg) -> {
          let error_model = Model(..model, loading: False, error: Some(error_msg))
          #(error_model, effect.none())
        }
      }
    }
  }
}

// VIEW ------------------------------------------------------------------------

fn render_elements(model: Model) -> List(Element(Msg)) {
  list.map(list.range(0, model.count), fn(el) {
    html.li([], [element.text(int.to_string(el))])
  })
}

fn buttons(model: Model) -> Element(Msg) {
  let count = model.count |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter (End-to-End Type Safe)")]),
    html.p([], [element.text("Count: " <> count)]),

    // Loading indicator
    case model.loading {
      True -> html.p([], [element.text("Loading...")])
      False -> html.div([], [])
    },

    // Error display
    case model.error {
      Some(error_msg) -> html.p([], [element.text("Error: " <> error_msg)])
      None -> html.div([], [])
    },

    // Last response info (for debugging type safety)
    case model.last_response {
      Some(response) -> html.p([], [
        element.text("Last update: " <> response.timestamp <> " | Previous: " <> case response.previous_count {
          Some(prev) -> int.to_string(prev)
          None -> "N/A"
        })
      ])
      None -> html.div([], [])
    },

    // Action buttons
    html.div([], [
      button.primary("+", Increment),
      button.secondary("-", Decrement),
      button.danger("Reset", Reset),
      button.secondary("Reload", LoadCounter),
    ])
  ])
}

fn view(model: Model) -> Element(Msg) {
  html.div([], [buttons(model), html.ul([], render_elements(model))])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.application(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
