// SHARED API TYPES ============================================================
// These types are shared between client and server to ensure end-to-end type safety

import gleam/option.{type Option}

// DOMAIN TYPES ----------------------------------------------------------------

/// Core counter state representation
pub type CounterState {
  CounterState(
    value: Int,
    last_updated: String,
    created_at: String,
  )
}

/// Statistics about counter usage
pub type CounterStats {
  CounterStats(
    total_increments: Int,
    total_decrements: Int,
    total_resets: Int,
    current_value: Int,
    last_action: Option(String),
  )
}

// API REQUEST TYPES -----------------------------------------------------------

/// All possible counter operations
pub type CounterRequest {
  IncrementRequest
  DecrementRequest
  ResetRequest
  SetValueRequest(value: Int)
  GetStatsRequest
}

/// Request metadata for logging and tracking
pub type RequestMetadata {
  RequestMetadata(
    timestamp: String,
    user_agent: Option(String),
    request_id: String,
  )
}

/// Complete API request with metadata
pub type ApiRequest {
  ApiRequest(
    counter_request: CounterRequest,
    metadata: Option(RequestMetadata),
  )
}

// API RESPONSE TYPES ----------------------------------------------------------

/// Counter operation response
pub type CounterResponse {
  CounterResponse(
    count: Int,
    timestamp: String,
    previous_count: Option(Int),
  )
}

/// Statistics response
pub type StatsResponse {
  StatsResponse(
    stats: CounterStats,
    timestamp: String,
  )
}

/// All possible successful API responses
pub type ApiResponse {
  CounterApiResponse(CounterResponse)
  StatsApiResponse(StatsResponse)
  HealthResponse(status: String, timestamp: String)
}

// ERROR TYPES -----------------------------------------------------------------

/// Validation errors for requests
pub type ValidationError {
  InvalidValue(field: String, reason: String)
  MissingField(field: String)
  InvalidFormat(field: String, expected: String)
}

/// Database operation errors
pub type DatabaseError {
  ConnectionError(String)
  QueryError(String)
  TransactionError(String)
  ConstraintViolation(String)
}

/// Network and HTTP errors
pub type NetworkError {
  RequestTimeout
  ConnectionFailed(String)
  InvalidResponse(String)
  HttpError(status: Int, message: String)
}

/// Business logic errors
pub type BusinessError {
  CounterLimitExceeded(limit: Int)
  InvalidOperation(reason: String)
  ResourceNotFound(resource: String)
}

/// All possible API errors
pub type ApiError {
  ValidationError(ValidationError)
  DatabaseError(DatabaseError)
  NetworkError(NetworkError)
  BusinessError(BusinessError)
  InternalError(String)
}

// RESULT TYPES ----------------------------------------------------------------

/// Standard API result type
pub type ApiResult(a) = Result(a, ApiError)

/// Response wrapper with metadata
pub type ApiResponseWrapper {
  ApiResponseWrapper(
    data: ApiResponse,
    metadata: ResponseMetadata,
  )
}

/// Response metadata
pub type ResponseMetadata {
  ResponseMetadata(
    timestamp: String,
    request_id: Option(String),
    processing_time_ms: Option(Int),
  )
}

// HEALTH CHECK TYPES ----------------------------------------------------------

/// Health check status
pub type HealthStatus {
  Healthy
  Degraded(reason: String)
  Unhealthy(reason: String)
}

/// Component health information
pub type ComponentHealth {
  ComponentHealth(
    name: String,
    status: HealthStatus,
    last_check: String,
    details: Option(String),
  )
}

/// Overall system health
pub type SystemHealth {
  SystemHealth(
    overall_status: HealthStatus,
    components: List(ComponentHealth),
    timestamp: String,
  )
}
