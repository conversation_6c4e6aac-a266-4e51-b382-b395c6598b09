// RIVA GLEAM SHARED PACKAGE ===================================================
// Main module for the shared types and utilities package

import gleam/option
import api_types
import codecs

// Re-export types and constructors
pub type CounterRequest = api_types.CounterRequest
pub type CounterResponse = api_types.CounterResponse
pub type CounterState = api_types.CounterState
pub type CounterStats = api_types.CounterStats
pub type ApiRequest = api_types.ApiRequest
pub type ApiResponse = api_types.ApiResponse
pub type ApiError = api_types.ApiError
pub type ApiResult(a) = api_types.ApiResult(a)



// Re-export constructors as functions
pub fn counter_response(count: Int, timestamp: String, previous_count: option.Option(Int)) -> CounterResponse {
  api_types.CounterResponse(count: count, timestamp: timestamp, previous_count: previous_count)
}

pub fn increment_request() -> CounterRequest {
  api_types.IncrementRequest
}

pub fn decrement_request() -> CounterRequest {
  api_types.DecrementRequest
}

pub fn reset_request() -> CounterRequest {
  api_types.ResetRequest
}

pub fn set_value_request(value: Int) -> CounterRequest {
  api_types.SetValueRequest(value)
}

pub fn get_stats_request() -> CounterRequest {
  api_types.GetStatsRequest
}

// Helper function to match request types
pub fn match_counter_request(
  request: CounterRequest,
  on_increment: fn() -> a,
  on_decrement: fn() -> a,
  on_reset: fn() -> a,
  on_set_value: fn(Int) -> a,
  on_get_stats: fn() -> a,
) -> a {
  case request {
    api_types.IncrementRequest -> on_increment()
    api_types.DecrementRequest -> on_decrement()
    api_types.ResetRequest -> on_reset()
    api_types.SetValueRequest(value) -> on_set_value(value)
    api_types.GetStatsRequest -> on_get_stats()
  }
}

// Re-export codec functions
pub const encode_counter_request = codecs.encode_counter_request
pub const encode_counter_request_string = codecs.encode_counter_request_string
pub const encode_counter_response = codecs.encode_counter_response
pub const encode_counter_response_string = codecs.encode_counter_response_string
pub const decode_counter_request_from_json = codecs.decode_counter_request_from_json
pub const decode_counter_response_from_json = codecs.decode_counter_response_from_json
