// RIVA GLEAM SHARED PACKAGE ===================================================
// Main module for the shared types and utilities package

import api_types
import codecs

// Re-export types
pub type CounterRequest = api_types.CounterRequest
pub type CounterResponse = api_types.CounterResponse
pub type CounterState = api_types.CounterState
pub type CounterStats = api_types.CounterStats
pub type ApiRequest = api_types.ApiRequest
pub type ApiResponse = api_types.ApiResponse
pub type ApiError = api_types.ApiError
pub type ApiResult(a) = api_types.ApiResult(a)

// Re-export codec functions
pub const encode_counter_request = codecs.encode_counter_request
pub const encode_counter_request_string = codecs.encode_counter_request_string
pub const encode_counter_response = codecs.encode_counter_response
pub const encode_counter_response_string = codecs.encode_counter_response_string
pub const decode_counter_request_from_json = codecs.decode_counter_request_from_json
pub const decode_counter_response_from_json = codecs.decode_counter_response_from_json
