// TYPE-SAFE JSON CODECS =======================================================
// Encode/decode functions for all API types with comprehensive error handling

import gleam/json
import gleam/dynamic/decode
import gleam/option.{None, Some}
import api_types.{
  type CounterRequest, type CounterResponse,
}

// COUNTER REQUEST CODECS ------------------------------------------------------

pub fn encode_counter_request(req: CounterRequest) -> json.Json {
  case req {
    api_types.IncrementRequest ->
      json.object([#("action", json.string("increment"))])

    api_types.DecrementRequest ->
      json.object([#("action", json.string("decrement"))])

    api_types.ResetRequest ->
      json.object([#("action", json.string("reset"))])

    api_types.SetValueRequest(value) ->
      json.object([
        #("action", json.string("set_value")),
        #("value", json.int(value)),
      ])

    api_types.GetStatsRequest ->
      json.object([#("action", json.string("get_stats"))])
  }
}

pub fn encode_counter_request_string(req: CounterRequest) -> String {
  encode_counter_request(req)
  |> json.to_string()
}

// Decoder for JSON strings
pub fn decode_counter_request_from_json(
  json_str: String,
) -> Result(CounterRequest, String) {
  let decoder = {
    use action <- decode.field("action", decode.string)
    case action {
      "increment" -> decode.success(api_types.IncrementRequest)
      "decrement" -> decode.success(api_types.DecrementRequest)
      "reset" -> decode.success(api_types.ResetRequest)
      "get_stats" -> decode.success(api_types.GetStatsRequest)
      "set_value" -> {
        use value <- decode.field("value", decode.int)
        decode.success(api_types.SetValueRequest(value))
      }
      _ -> decode.failure(api_types.IncrementRequest, "Invalid action: " <> action)
    }
  }

  case json.parse(json_str, decoder) {
    Ok(request) -> Ok(request)
    Error(_) -> Error("Failed to decode counter request")
  }
}

// COUNTER RESPONSE CODECS -----------------------------------------------------

pub fn encode_counter_response(resp: CounterResponse) -> json.Json {
  json.object([
    #("count", json.int(resp.count)),
    #("timestamp", json.string(resp.timestamp)),
    #(
      "previous_count",
      case resp.previous_count {
        None -> json.null()
        Some(prev) -> json.int(prev)
      },
    ),
  ])
}

pub fn encode_counter_response_string(resp: CounterResponse) -> String {
  encode_counter_response(resp)
  |> json.to_string()
}

// Decoder for JSON strings
pub fn decode_counter_response_from_json(
  json_str: String,
) -> Result(CounterResponse, String) {
  let decoder = {
    use count <- decode.field("count", decode.int)
    use timestamp <- decode.field("timestamp", decode.string)
    use previous_count <- decode.field("previous_count", decode.optional(decode.int))
    decode.success(api_types.CounterResponse(
      count: count,
      timestamp: timestamp,
      previous_count: previous_count,
    ))
  }

  case json.parse(json_str, decoder) {
    Ok(response) -> Ok(response)
    Error(_) -> Error("Failed to decode counter response")
  }
}
