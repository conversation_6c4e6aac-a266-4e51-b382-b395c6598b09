import gleeunit
import gleeunit/should
import gleam/option.{None, Some}
import codecs
import api_types

pub fn main() {
  gleeunit.main()
}

// Test encoding and decoding counter requests
pub fn encode_increment_request_test() {
  let request = api_types.IncrementRequest
  let json_string = codecs.encode_counter_request_string(request)
  should.equal(json_string, "{\"action\":\"increment\"}")
}

pub fn encode_set_value_request_test() {
  let request = api_types.SetValueRequest(42)
  let json_string = codecs.encode_counter_request_string(request)
  should.equal(json_string, "{\"action\":\"set_value\",\"value\":42}")
}

pub fn decode_increment_request_test() {
  let json_string = "{\"action\":\"increment\"}"
  let result = codecs.decode_counter_request_from_json(json_string)
  should.equal(result, Ok(api_types.IncrementRequest))
}

pub fn decode_set_value_request_test() {
  let json_string = "{\"action\":\"set_value\",\"value\":42}"
  let result = codecs.decode_counter_request_from_json(json_string)
  should.equal(result, Ok(api_types.SetValueRequest(42)))
}

pub fn decode_invalid_request_test() {
  let json_string = "{\"action\":\"invalid\"}"
  let result = codecs.decode_counter_request_from_json(json_string)
  should.be_error(result)
}

// Test encoding and decoding counter responses
pub fn encode_counter_response_test() {
  let response = api_types.CounterResponse(
    count: 5,
    timestamp: "2024-01-01T00:00:00Z",
    previous_count: Some(4),
  )
  let json_string = codecs.encode_counter_response_string(response)
  should.equal(json_string, "{\"count\":5,\"timestamp\":\"2024-01-01T00:00:00Z\",\"previous_count\":4}")
}

pub fn decode_counter_response_test() {
  let json_string = "{\"count\":5,\"timestamp\":\"2024-01-01T00:00:00Z\",\"previous_count\":4}"
  let result = codecs.decode_counter_response_from_json(json_string)
  should.equal(result, Ok(api_types.CounterResponse(
    count: 5,
    timestamp: "2024-01-01T00:00:00Z",
    previous_count: Some(4),
  )))
}

pub fn decode_counter_response_with_null_previous_test() {
  let json_string = "{\"count\":5,\"timestamp\":\"2024-01-01T00:00:00Z\",\"previous_count\":null}"
  let result = codecs.decode_counter_response_from_json(json_string)
  should.equal(result, Ok(api_types.CounterResponse(
    count: 5,
    timestamp: "2024-01-01T00:00:00Z",
    previous_count: None,
  )))
}

// Test round-trip encoding/decoding
pub fn round_trip_increment_test() {
  let original = api_types.IncrementRequest
  let json_string = codecs.encode_counter_request_string(original)
  let decoded = codecs.decode_counter_request_from_json(json_string)
  should.equal(decoded, Ok(original))
}

pub fn round_trip_set_value_test() {
  let original = api_types.SetValueRequest(123)
  let json_string = codecs.encode_counter_request_string(original)
  let decoded = codecs.decode_counter_request_from_json(json_string)
  should.equal(decoded, Ok(original))
}

pub fn round_trip_response_test() {
  let original = api_types.CounterResponse(
    count: 10,
    timestamp: "2024-06-28T12:00:00Z",
    previous_count: Some(9),
  )
  let json_string = codecs.encode_counter_response_string(original)
  let decoded = codecs.decode_counter_response_from_json(json_string)
  should.equal(decoded, Ok(original))
}
